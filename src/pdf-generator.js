import puppeteer from 'puppeteer';
import { marked } from 'marked';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class PDFGenerator {
  constructor() {
    this.browser = null;
  }

  async getBrowser() {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
        timeout: 60000 // 60 seconds timeout
      });
    }
    return this.browser;
  }

  async generatePDF(config) {
    const {
      content,
      template,
      images = [],
      layout = {},
      outputPath,
      options = {},
      templateEngine
    } = config;

    try {
      // Generate HTML content
      const htmlContent = await this.generateHTMLContent({
        content,
        template,
        images,
        options,
        templateEngine
      });

      // Create PDF using Puppeteer
      const browser = await this.getBrowser();
      const page = await browser.newPage();

      // Set content
      await page.setContent(htmlContent, {
        waitUntil: 'networkidle0',
        timeout: 60000 // 60 seconds timeout
      });

      // Configure PDF options
      const pdfOptions = {
        path: outputPath,
        format: layout.format || 'A4',
        landscape: layout.orientation === 'landscape',
        margin: {
          top: layout.margin?.top || '1in',
          right: layout.margin?.right || '1in',
          bottom: layout.margin?.bottom || '1in',
          left: layout.margin?.left || '1in'
        },
        printBackground: true,
        preferCSSPageSize: true
      };

      // Add header and footer if specified
      if (options.header) {
        pdfOptions.headerTemplate = this.generateHeaderFooter(options.header, true);
        pdfOptions.displayHeaderFooter = true;
      }

      if (options.footer || options.page_numbers) {
        let footerContent = options.footer || '';
        if (options.page_numbers) {
          footerContent += `<div style="font-size: 10px; text-align: center; width: 100%;">
            Page <span class="pageNumber"></span> of <span class="totalPages"></span>
          </div>`;
        }
        pdfOptions.footerTemplate = this.generateHeaderFooter(footerContent, false);
        pdfOptions.displayHeaderFooter = true;
      }

      // Generate PDF
      await page.pdf(pdfOptions);
      await page.close();

      // Get file stats
      const stats = await fs.stat(outputPath);
      
      return {
        outputPath,
        size: this.formatFileSize(stats.size),
        pages: await this.getPDFPageCount(outputPath)
      };

    } catch (error) {
      throw new Error(`PDF generation failed: ${error.message}`);
    }
  }

  async generateHTML(config) {
    const {
      content,
      template,
      outputPath,
      templateEngine
    } = config;

    try {
      const htmlContent = await this.generateHTMLContent({
        content,
        template,
        images: [],
        options: {},
        templateEngine
      });

      await fs.writeFile(outputPath, htmlContent, 'utf8');

      return { outputPath };
    } catch (error) {
      throw new Error(`HTML generation failed: ${error.message}`);
    }
  }

  async generateHTMLContent({ content, template, images, options, templateEngine }) {
    // Convert markdown to HTML if needed
    let htmlContent = content;
    if (this.isMarkdown(content)) {
      htmlContent = marked(content);
    }

    // Process images
    htmlContent = await this.processImages(htmlContent, images);

    // Apply template
    const templateData = await templateEngine.getTemplate(template);
    const finalHTML = await templateEngine.applyTemplate(templateData, {
      content: htmlContent,
      title: options.title || 'Document',
      author: options.author || '',
      subject: options.subject || '',
      keywords: options.keywords || ''
    });

    return finalHTML;
  }

  async processImages(htmlContent, images) {
    // Process embedded images and convert to base64 if needed
    for (const image of images) {
      if (image.path && await fs.pathExists(image.path)) {
        const imageBuffer = await fs.readFile(image.path);
        const mimeType = this.getMimeType(image.path);
        const base64 = imageBuffer.toString('base64');
        const dataUrl = `data:${mimeType};base64,${base64}`;
        
        // Replace image references in HTML
        const imgTag = `<img src="${dataUrl}" alt="${image.alt || ''}"${
          image.width ? ` width="${image.width}"` : ''
        }${image.height ? ` height="${image.height}"` : ''}${
          image.position ? ` style="float: ${image.position};"` : ''
        }>`;
        
        // Simple replacement - in production, you might want more sophisticated matching
        htmlContent = htmlContent.replace(
          new RegExp(`\\[${image.alt || 'image'}\\]\\(${image.path}\\)`, 'g'),
          imgTag
        );
      }
    }

    return htmlContent;
  }

  generateHeaderFooter(content, isHeader) {
    return `
      <div style="font-size: 10px; margin: 0 1in; ${isHeader ? 'margin-top: 0.5in;' : 'margin-bottom: 0.5in;'}">
        ${content}
      </div>
    `;
  }

  isMarkdown(content) {
    // Simple heuristic to detect markdown
    return content.includes('# ') || content.includes('## ') || 
           content.includes('**') || content.includes('*') ||
           content.includes('[') && content.includes('](');
  }

  getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.webp': 'image/webp'
    };
    return mimeTypes[ext] || 'image/png';
  }

  formatFileSize(bytes) {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  async getPDFPageCount(filePath) {
    // Simple estimation - in production, you might want to use a PDF library
    const stats = await fs.stat(filePath);
    return Math.ceil(stats.size / 50000); // Rough estimate
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }
}
