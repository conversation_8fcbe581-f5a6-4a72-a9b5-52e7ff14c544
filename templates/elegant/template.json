{"name": "elegant", "description": "Elegant template with serif fonts and warm colors", "css": "\n:root {\n  --primary-color: #e74c3c;\n  --secondary-color: #f39c12;\n  --text-color: #2c3e50;\n  --background-color: #ffffff;\n}\n\nbody {\n  font-family: 'Georgia', serif;\n  font-size: 11pt;\n  line-height: 1.8;\n  color: var(--text-color);\n}\n\nh1 {\n  color: var(--primary-color);\n  font-size: 2.5em;\n  text-align: center;\n  border-bottom: 3px solid var(--secondary-color);\n  padding-bottom: 0.5em;\n}\n\nh2 {\n  color: var(--secondary-color);\n  font-size: 1.6em;\n}\n\np {\n  text-align: justify;\n  margin-bottom: 1.2em;\n}\n", "html": "\n<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{title}}</title>\n    <meta name=\"author\" content=\"{{author}}\">\n    <meta name=\"subject\" content=\"{{subject}}\">\n    <meta name=\"keywords\" content=\"{{keywords}}\">\n    <style>\n        {{{css}}}\n    </style>\n</head>\n<body>\n    <div class=\"document\">\n        {{{content}}}\n    </div>\n</body>\n</html>\n", "layoutConfig": {}}