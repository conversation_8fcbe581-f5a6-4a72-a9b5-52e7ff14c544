
/* Default PDF Template Styles */
:root {
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --text-color: #2c3e50;
  --background-color: #ffffff;
  --border-color: #bdc3c7;
  --code-background: #f8f9fa;
  --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-size: 12pt;
  --line-height: 1.6;
}

* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size);
  line-height: var(--line-height);
  color: var(--text-color);
  background-color: var(--background-color);
  margin: 0;
  padding: 0;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--primary-color);
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  page-break-after: avoid;
}

h1 {
  font-size: 2.2em;
  border-bottom: 3px solid var(--secondary-color);
  padding-bottom: 0.3em;
}

h2 {
  font-size: 1.8em;
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 0.2em;
}

h3 {
  font-size: 1.4em;
}

h4 {
  font-size: 1.2em;
}

p {
  margin-bottom: 1em;
  text-align: justify;
}

/* Lists */
ul, ol {
  margin-bottom: 1em;
  padding-left: 2em;
}

li {
  margin-bottom: 0.3em;
}

/* Code */
code {
  background-color: var(--code-background);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

pre {
  background-color: var(--code-background);
  padding: 1em;
  border-radius: 5px;
  overflow-x: auto;
  margin-bottom: 1em;
  border-left: 4px solid var(--secondary-color);
}

pre code {
  background: none;
  padding: 0;
}

/* Tables */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1em;
}

th, td {
  border: 1px solid var(--border-color);
  padding: 0.5em;
  text-align: left;
}

th {
  background-color: var(--code-background);
  font-weight: 600;
  color: var(--primary-color);
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  margin: 1em 0;
  border-radius: 5px;
}

/* Blockquotes */
blockquote {
  border-left: 4px solid var(--secondary-color);
  margin: 1em 0;
  padding-left: 1em;
  font-style: italic;
  color: #666;
}

/* Page breaks */
.page-break {
  page-break-before: always;
}

.no-break {
  page-break-inside: avoid;
}

/* Print-specific styles */
@media print {
  body {
    font-size: 11pt;
  }
  
  h1, h2, h3 {
    page-break-after: avoid;
  }
  
  img {
    page-break-inside: avoid;
  }
  
  table {
    page-break-inside: avoid;
  }
}

/* Custom utility classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mt-1 { margin-top: 1em; }
.mt-2 { margin-top: 2em; }
.mb-1 { margin-bottom: 1em; }
.mb-2 { margin-bottom: 2em; }

.border {
  border: 1px solid var(--border-color);
  padding: 1em;
  border-radius: 5px;
}

.highlight {
  background-color: #fff3cd;
  padding: 0.5em;
  border-left: 4px solid #ffc107;
  margin: 1em 0;
}
