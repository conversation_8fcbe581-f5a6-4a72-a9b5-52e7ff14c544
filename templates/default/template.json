{"name": "default", "description": "Clean, professional document template", "css": "\n/* Default PDF Template Styles */\n:root {\n  --primary-color: #2c3e50;\n  --secondary-color: #3498db;\n  --text-color: #2c3e50;\n  --background-color: #ffffff;\n  --border-color: #bdc3c7;\n  --code-background: #f8f9fa;\n  --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  --font-size: 12pt;\n  --line-height: 1.6;\n}\n\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: var(--font-family);\n  font-size: var(--font-size);\n  line-height: var(--line-height);\n  color: var(--text-color);\n  background-color: var(--background-color);\n  margin: 0;\n  padding: 0;\n}\n\n/* Typography */\nh1, h2, h3, h4, h5, h6 {\n  color: var(--primary-color);\n  margin-top: 1.5em;\n  margin-bottom: 0.5em;\n  font-weight: 600;\n  page-break-after: avoid;\n}\n\nh1 {\n  font-size: 2.2em;\n  border-bottom: 3px solid var(--secondary-color);\n  padding-bottom: 0.3em;\n}\n\nh2 {\n  font-size: 1.8em;\n  border-bottom: 2px solid var(--border-color);\n  padding-bottom: 0.2em;\n}\n\nh3 {\n  font-size: 1.4em;\n}\n\nh4 {\n  font-size: 1.2em;\n}\n\np {\n  margin-bottom: 1em;\n  text-align: justify;\n}\n\n/* Lists */\nul, ol {\n  margin-bottom: 1em;\n  padding-left: 2em;\n}\n\nli {\n  margin-bottom: 0.3em;\n}\n\n/* Code */\ncode {\n  background-color: var(--code-background);\n  padding: 0.2em 0.4em;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  font-size: 0.9em;\n}\n\npre {\n  background-color: var(--code-background);\n  padding: 1em;\n  border-radius: 5px;\n  overflow-x: auto;\n  margin-bottom: 1em;\n  border-left: 4px solid var(--secondary-color);\n}\n\npre code {\n  background: none;\n  padding: 0;\n}\n\n/* Tables */\ntable {\n  width: 100%;\n  border-collapse: collapse;\n  margin-bottom: 1em;\n}\n\nth, td {\n  border: 1px solid var(--border-color);\n  padding: 0.5em;\n  text-align: left;\n}\n\nth {\n  background-color: var(--code-background);\n  font-weight: 600;\n  color: var(--primary-color);\n}\n\n/* Images */\nimg {\n  max-width: 100%;\n  height: auto;\n  margin: 1em 0;\n  border-radius: 5px;\n}\n\n/* Blockquotes */\nblockquote {\n  border-left: 4px solid var(--secondary-color);\n  margin: 1em 0;\n  padding-left: 1em;\n  font-style: italic;\n  color: #666;\n}\n\n/* Page breaks */\n.page-break {\n  page-break-before: always;\n}\n\n.no-break {\n  page-break-inside: avoid;\n}\n\n/* Print-specific styles */\n@media print {\n  body {\n    font-size: 11pt;\n  }\n  \n  h1, h2, h3 {\n    page-break-after: avoid;\n  }\n  \n  img {\n    page-break-inside: avoid;\n  }\n  \n  table {\n    page-break-inside: avoid;\n  }\n}\n\n/* Custom utility classes */\n.text-center { text-align: center; }\n.text-right { text-align: right; }\n.text-left { text-align: left; }\n\n.mt-1 { margin-top: 1em; }\n.mt-2 { margin-top: 2em; }\n.mb-1 { margin-bottom: 1em; }\n.mb-2 { margin-bottom: 2em; }\n\n.border {\n  border: 1px solid var(--border-color);\n  padding: 1em;\n  border-radius: 5px;\n}\n\n.highlight {\n  background-color: #fff3cd;\n  padding: 0.5em;\n  border-left: 4px solid #ffc107;\n  margin: 1em 0;\n}\n", "html": "\n<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{title}}</title>\n    <meta name=\"author\" content=\"{{author}}\">\n    <meta name=\"subject\" content=\"{{subject}}\">\n    <meta name=\"keywords\" content=\"{{keywords}}\">\n    <style>\n        {{{css}}}\n    </style>\n</head>\n<body>\n    <div class=\"document\">\n        {{{content}}}\n    </div>\n</body>\n</html>\n", "layoutConfig": {"format": "A4", "orientation": "portrait", "margin": {"top": "1in", "right": "1in", "bottom": "1in", "left": "1in"}}}